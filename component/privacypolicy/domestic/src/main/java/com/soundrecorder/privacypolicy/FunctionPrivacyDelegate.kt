/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FunctionPrivacyDelegate.kt
 ** Description : The Delegate of Function Privacy Dialog
 ** Version     : 1.0
 ** Date        : 2025/06/04
 ** Author      : <PERSON><PERSON><PERSON>.<EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  Jiafei.Liu     2025/06/04     1.0      create
 ***********************************************************************/

package com.soundrecorder.privacypolicy

import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.modulerouter.privacyPolicy.IFunctionPrivacyCallback
import com.soundrecorder.modulerouter.privacyPolicy.IFunctionPrivacyDelegate

class FunctionPrivacyDelegate(private val funcType: Int) : IFunctionPrivacyDelegate, DefaultLifecycleObserver {
    private var functionPrivacyDialog: FunctionPrivacyDialog? = null


    override fun showFunctionPrivacyDialog(activity: FragmentActivity, callback: IFunctionPrivacyCallback?) {
        if (functionPrivacyDialog == null) {
            functionPrivacyDialog = FunctionPrivacyDialog(funcType)
            activity.lifecycle.addObserver(this)
        }

        functionPrivacyDialog?.showFunctionPrivacyDialog(
            activity,
            confirmCallback = {
                // 设置权限
                PermissionUtils.setFuncTypePermission(funcType, true)
                // 调用成功回调
                callback?.onPrivacyAgreed()
            },
            cancelCallback = {
                // 调用拒绝回调
                callback?.onPrivacyRejected()
            }
        )
    }

    override fun dismissFunctionPrivacyDialog() {
        functionPrivacyDialog?.releaseDialog()
        functionPrivacyDialog = null
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        owner.lifecycle.removeObserver(this)
        dismissFunctionPrivacyDialog()
    }
}