apply from: "../../common_build.gradle"

android {
    namespace 'com.soundrecorder.share'
}

dependencies {
    implementation project(':common:libbase')
    implementation project(':common:libcommon')
    implementation project(':common:modulerouter')

    // Koin for Android
    implementation(libs.koin)
    implementation libs.androidx.core.ktx
    implementation(libs.heytap.nearx.http) {
        /*#4118634: clientId sdk引入了android.permission.READ_PHONE_STATE权限，外销需去掉该sdk，否则有安规问题*/
        exclude group: 'com.heytap.baselib', module: 'clientId'
    }
    // add for AES\RSA
    implementation libs.commons.codec
    implementation libs.gson
}