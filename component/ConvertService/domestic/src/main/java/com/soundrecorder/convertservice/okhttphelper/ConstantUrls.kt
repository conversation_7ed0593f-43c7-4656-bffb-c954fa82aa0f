/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConstantUrls
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.okhttphelper

import com.soundrecorder.convertservice.BuildConfig
import com.soundrecorder.convertservice.okhttphelper.SignatureKeyUtils.turnToV2

object ConstantUrls {

    val BACKENDURL = turnToV2(SignatureKeyUtils.getString(BuildConfig.enbackUrl))

//    val BACKENDURL = "http://atot-test.wanyol.com/v2"
    val BACK_GETPREURL = "$BACKENDURL/getPreSignedUrl"
    val BACK_ABORT_TASK = "$BACKENDURL/abortTask"
    val BACK_ADD_TASK = "$BACKENDURL/addTask"
    val BACK_QUERY_TASK = "$BACKENDURL/queryTask"
    val BACK_GET_UPLOADRESULT = "$BACKENDURL/getUploadResult"

    /**
     * 提取关键词
     */
    val BACK_EXTRACT_KEY_WORDS = "$BACKENDURL/segmentAndScore"

    val PUBKEY = SignatureKeyUtils.getString(BuildConfig.pub)
    val SECRET = SignatureKeyUtils.getString(BuildConfig.sec)
}