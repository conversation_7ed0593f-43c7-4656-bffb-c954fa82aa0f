/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  IOkhttpHelper
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.okhttphelper

import android.net.Uri
import com.soundrecorder.common.databean.KeyWord
import com.soundrecorder.convertservice.bean.*

interface IOkhttpHelper {

    fun getPresignedURLs(
        requestId: String,
        key: String,
        n: Int,
        uploadId: String
    ): BaseResponse<BeanGetPresignedURLs>

/*    fun getPresignedURLs(req: RequestGetPresignedURLs): BaseResponse<BeanGetPresignedURLs> {
        return getPresignedURLs(req.requestId, req.key, req.n)
    }*/

    fun uploadOCS(
        requestId: String,
        presignedOCSURL: String,
        uri: Uri
    ): BaseResponse<Boolean>


    fun uploadOCS(
        requestId: String,
        presignedOCSURL: String,
        uri: Uri,
        begingOffset: Int,
        endOffset: Int
    ): BaseResponse<String>

    fun getUploadResult(
        requestId: String,
        uploadId: String?,
        key: String,
        abortFlag: Boolean = false,
        partETags: List<RequestGetUploadResult.ETags>? = null
    ): BaseResponse<BeanUploadResult>

/*
    fun getUploadResult(req: RequestGetUploadResult, backendURL: String): BaseResponse<BeanUploadResult> {
        return getUploadResult(req.requestId, req.uploadId, req.key, req.abortFlag, req.partETags)
    }
*/

    @Suppress("LongParameterList")
    fun addTask(
        requestId: String,
        objectURL: String,
        audioTimeLine: String,
        codecFlag: Boolean?,
        duration: Long?,
        audioAllowedSave: Boolean?,
        roleType: Int?,
        roleNum: Int?
    ): BaseResponse<BeanConvert>

    fun queryTask(
        requestId: String,
        taskId: String,
        duration: Long?,
        keyWord: Int
    ): BaseResponse<BeanConvert>

    fun abortTask(requestId: String, taskId: String): BaseResponse<BeanConvert>

    /**
     * 提取关键词
     */
    fun extractKeyWords(requestId: String, texts: List<String>): BaseData<List<KeyWord>>
}