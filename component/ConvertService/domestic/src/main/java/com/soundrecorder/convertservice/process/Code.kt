/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  Code
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.process

class Code {
    companion object {
        const val NORMAL = -1

        const val TASKCODE_0 = 0
        const val HTTP_OK = 200
        const val TASKCODE_200 = 200
        const val TASKCODE_1000 = 1000
        const val TASKCODE_1001 = 1001
        const val TASKCODE_1002 = 1002
        const val TASKCODE_1003 = 1003
        const val TASKCODE_1004 = 1004
        const val TASKCODE_1009 = 1009
        const val TASKCODE_1011 = 1011

        const val TASKCODE_2001 = 2001
        const val TASKCODE_2002 = 2002
        const val TASKCODE_2003 = 2003

        const val QUERY_TASK_SUC = 2000
        const val QUERY_TASK_FAIL = 2001
        const val QUERY_TASK_DOING = 2002

        const val EXCEPTION = 100001
        const val HTTP_GETURL = 100001
        const val HTTP_UPLOADOCS = 100002
        const val HTTP_GETUPLOADRESULT = 100003
        const val HTTP_ADDTASK = 100004
        const val HTTP_QUERYTASK = 100005
        const val HTTP_ABORTTASK = 100006
        const val HTTP_ABORTUPLOAD = 100007

        const val DATA_GETURL = 200001
        const val DATA_UPLOADOCS = 200002
        const val DATA_GETUPLOADRESULT = 200003
        const val DATA_ADDTASK = 200004
        const val DATA_QUERYTASK = 200005
        const val DATA_ABORTTASK = 200006
        const val DATA_ABORTUPLOAD = 200007

        const val REACH_RETRY_LIMIT = 300001
        const val QUERYTASK_TIMEOUT = 300004
        const val CONVERT_TASKID_IS_NULL = 300005
        const val OBJECTURL_IS_NULL = 300006
        const val LOCAL_URL_WRONG = 300007
        const val REQUESTID_IS_NULL = 300008
        const val INSERT_DB_WRONG = 300009
        const val LOCATION_URL_WRONG = 300010
        const val UPLOADSTATUS_ISNOT_SUC = 300011
        const val DURATION_WRONG = 300012


        const val NETWORK_WRONG = 400001
        const val FILEFORMAT_WRONG = 400002
        const val FILESIZE_WRONG = 400003
        const val FILEDURATION_WRONG = 400004


        const val ENCYPT_CODE_ERROR = 500001


        const val EXCEPTION_INIT_REQ = 600001
        const val EXCEPTION_INSERT_REQ = 600002

        const val DIRTY_DATA = 700001
    }
}