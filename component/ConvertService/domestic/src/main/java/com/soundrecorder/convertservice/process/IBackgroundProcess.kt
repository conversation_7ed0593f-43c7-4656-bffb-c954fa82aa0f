/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  IBackgroundProcess
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.process

import com.soundrecorder.common.databean.ConvertRecord
import com.soundrecorder.convertservice.convert.IConvertCallback

interface IBackgroundProcess {
    val nextProcess: IBackgroundProcess?

    var convertCallback: IConvertCallback?

    fun process(req: ConvertRecord): Boolean
}