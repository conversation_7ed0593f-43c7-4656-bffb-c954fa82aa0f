/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  HeaderHelper
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.okhttphelper

import com.oplus.recorderlog.util.CommonFlavor
import com.soundrecorder.base.utils.AppUtil
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.LanguageUtil
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.base.utils.OS12FeatureUtil.isFindX4
import com.soundrecorder.base.utils.OpenIdUtils

object HeaderHelper {

    /**
     * findx4 use serverplan different from others
     */
    val model = if (isFindX4()) "Find" else BaseUtil.getConfigFromSystem(Constants.ATTR_MODEL)
//    val model = "Find"

    val otaVersion = BaseUtil.getConfigFromSystem(Constants.ATTR_OTAVERSION)
    val romVersion = BaseUtil.getConfigFromSystem(Constants.ATTR_ROMVERSION)
    val colorOSVersion = BaseUtil.getConfigFromSystem(Constants.ATTR_COLOROSVERSION)
    val androidVersion = BaseUtil.getConfigFromSystem(Constants.ATTR_ANDROIDVERSION)
    val uRegion = BaseUtil.getConfigFromSystem(Constants.ATTR_UREGION)
    val uLang = LanguageUtil.getCurrentLanguageFromSystem()
    val clientVersionCode = AppUtil.getAppVersion()
    val clientPackage = AppUtil.getAppName() ?: ""
    val duid = OpenIdUtils.INSTANCE.duid
    val serverPlanCode = getServerPlanCode()
    val brand = CommonFlavor.getInstance().flavorB?.toLowerCase() ?: ""

    @JvmName("getServerPlanCode1")
    fun getServerPlanCode(): Int {
        return 3
    }
}