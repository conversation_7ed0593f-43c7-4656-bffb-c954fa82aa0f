/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.audio.setting

import android.os.Bundle
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.preference.Preference
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.coui.appcompat.preference.COUIPreferenceFragment
import com.coui.appcompat.seekbar.COUISectionSeekBar
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.playback.PlaybackActivityViewModel
import com.soundrecorder.playback.R
import com.soundrecorder.playback.audio.setting.PlaySettingDialogFragment.Companion.SPEED_HALF
import com.soundrecorder.playback.audio.setting.PlaySettingDialogFragment.Companion.SPEED_ONE
import com.soundrecorder.playback.audio.setting.PlaySettingDialogFragment.Companion.SPEED_ONE_HALF
import com.soundrecorder.playback.audio.setting.PlaySettingDialogFragment.Companion.SPEED_TWO
import com.soundrecorder.playback.view.PlaySettingFooterPreference
import com.soundrecorder.playback.view.PlaySettingFooterPreference.OnBindOrClickListener
import oplus.multimedia.soundrecorder.playback.mute.MuteConstants.PLAY_INDEX_DEFAULT
import oplus.multimedia.soundrecorder.playback.mute.MuteDataState

class PlaySettingDialogFragment : COUIPanelFragment(),
    PlaySettingDialogPreferenceFragment.OnChoiceSelectedCallBack { //, LifecycleObserver

    companion object {
        private const val TAG = "PlaySettingDialogFragment"
        const val FRAGMENT_TAG = "PlaySettingDialogFragment"
        const val SPEED_HALF = "0.5x"
        const val SPEED_ONE = "1.0x"
        const val SPEED_ONE_HALF = "1.5x"
        const val SPEED_TWO = "2.0x"
    }

    private var mPreferenceDialog = PlaySettingDialogPreferenceFragment()
    var mViewModel: PlaybackActivityViewModel? = null
    var menuItemReset: MenuItem? = null
    var menuItemComplete: MenuItem? = null
    private var currentSpeedIndex = 1
    private var muteEnabled = false
    private val mMuteEnableObserver by lazy {
        Observer<Boolean> {
            DebugUtil.d(TAG, "mMuteEnableObserver, value is $it")
            setSwitchChecked(it)
        }
    }

    private val mObserver = Observer<Int> {
        DebugUtil.d(TAG, "mObserver, value is $it")
        when (it) {
            MuteDataState.MUTE_LOAD_STATE_INIT,
            MuteDataState.MUTE_LOAD_STATE_PREPARING,
            MuteDataState.MUTE_LOAD_STATE_LOADING_FROM_CACHE,
            MuteDataState.MUTE_LOAD_STATE_LOADING_FROM_ORIGIN -> {
                setSwitchItemEnable(false)
            }

            MuteDataState.MUTE_LOAD_STATE_COMPLETED -> {
                setSwitchItemEnable(true)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 第一个parentFragment是dialogFragment，第二个是播放containerFragment
        parentFragment?.parentFragment?.let {
            mViewModel = ViewModelProvider(it)[PlaybackActivityViewModel::class.java]
        }
    }

    private fun initObserves() {
        mViewModel?.muteDataManager?.let { muteDataManager ->
            muteDataManager.getLoadingState()?.observe(viewLifecycleOwner, mObserver)
            muteDataManager.muteEnable.observe(viewLifecycleOwner, mMuteEnableObserver)
        }
        mPreferenceDialog.setOnChoiceSelectedCallBack(this)
        currentSpeedIndex = mViewModel?.playerController?.playSpeedIndex?.value ?: 1
        muteEnabled = mViewModel?.muteDataManager?.muteEnable?.value ?: false
    }


    override fun initView(panelView: View?) {
        super.initView(panelView)
        initObserves()
        initToolBar()
        initPreference()
        initDialogContent(panelView)
    }

    private fun initToolBar() {
        toolbar = toolbar?.apply {
//            visibility = View.INVISIBLE
            title = resources.getString(com.soundrecorder.common.R.string.play_setting_title)
            isTitleCenterStyle = true
            inflateMenu(R.menu.menu_cancel)
            menuItemReset = menu.findItem(R.id.item_cancel_setting_dialog).apply {
                setOnMenuItemClickListener {
                    restoreChoice()
                    true
                }
            }
            menuItemComplete = menu.findItem(R.id.item_save_setting_dialog).apply {
                setOnMenuItemClickListener {
                    mViewModel?.muteDataManager?.apply {
                        if (getLoadingState()?.value == MuteDataState.MUTE_LOAD_STATE_COMPLETED) {
                            if (muteEnabled) {
                                enableMuteButton()
                            } else {
                                disableMuteEnable()
                            }
                            val state =
                                if (muteEnabled) RecorderUserAction.VALUE_SKIP_MUTE_OPEN else RecorderUserAction.VALUE_SKIP_MUTE_CLOSE
                            BuryingPoint.addSkipMuteSwitch(state)
                        } else {
                            enableMuteButton()
                        }
                    }
                    mViewModel?.playerController?.playSpeedIndex?.let { speedIndexLiveData ->
                        if (currentSpeedIndex != speedIndexLiveData.value) {
                            speedIndexLiveData.setValue(currentSpeedIndex)
                        }
                    }
                    BuryingPoint.addMultipleSpeed(currentSpeedIndex)
                    dismissDialog()
                    true
                }
            }
        }
    }

    private fun initPreference() {
        childFragmentManager.beginTransaction().replace(contentResId, mPreferenceDialog).commit()
    }

    private fun initDialogContent(panelView: View?) {
        hideDragView()
    }

    private fun initStatus() {
        mViewModel?.let {
            DebugUtil.d(TAG, "initStatus play speed ${it.playerController.playSpeedIndex.value}")
            mPreferenceDialog.setChoiceSelected(it.playerController.playSpeedIndex.value ?: NumberConstant.NUM_1)
            mPreferenceDialog.setSwitchChecked(it.muteDataManager?.muteEnable?.value == true)
        }
    }

    private fun restoreChoice() {
        onResetClick()
        mPreferenceDialog.setSwitchChecked(false)
        mPreferenceDialog.setChoiceSelected(NumberConstant.NUM_1)
        BuryingPoint.addMultipleSpeed(NumberConstant.NUM_1)
        BuryingPoint.addSkipMuteSwitch(RecorderUserAction.VALUE_SKIP_MUTE_CLOSE)
        BuryingPoint.addRestoreAll()
    }

    override fun onShow(isShowOnFirstPanel: Boolean?) {
        super.onShow(isShowOnFirstPanel)
        //设置dialogFragment的背景
        ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)?.setPanelBackgroundTintColor(
            COUIContextUtil.getAttrColor(
                context,
                com.support.appcompat.R.attr.couiColorSurfaceWithCard
            )
        )
    }

    private fun setSwitchItemEnable(enable: Boolean) {
        mPreferenceDialog.setSwitchEnable(enable)
    }

    private fun setSwitchChecked(checked: Boolean) {
        mPreferenceDialog.setSwitchChecked(checked)
    }

    override fun onCheckMuteEnable(): Boolean {
        return mViewModel?.muteDataManager?.muteEnable?.value == true
    }

    override fun onSwitchChanged(checked: Boolean) {
        muteEnabled = checked
        setRestoreTextViewEnable()
    }

    override fun onSpeedChanged(position: Int) {
        currentSpeedIndex = position
        setRestoreTextViewEnable()
    }

    override fun onDefaultValueSelected() {
        setRestoreTextViewEnable()
    }

    override fun onPreferenceAdded() {
        when (mViewModel?.muteDataManager?.getLoadingState()?.value) {
            MuteDataState.MUTE_LOAD_STATE_COMPLETED -> {
                setSwitchItemEnable(true)
            }

            else -> {
                setSwitchItemEnable(false)
            }
        }
        initStatus()
    }

    private fun setRestoreTextViewEnable() {
        mPreferenceDialog.setRestoreTextViewEnable()
    }

    fun getTextViewEnable(): Boolean {
        return (currentSpeedIndex != PLAY_INDEX_DEFAULT || muteEnabled)
    }

    fun getCompleteBtnEnable(): Boolean {
        return (currentSpeedIndex != mViewModel?.playerController?.playSpeedIndex?.value ||
                muteEnabled != mViewModel?.muteDataManager?.muteEnable?.value)
    }

    private fun onResetClick() {
        muteEnabled = false
        currentSpeedIndex = PLAY_INDEX_DEFAULT
        setRestoreTextViewEnable()
    }

    private fun dismissDialog() {
        (parentFragment as? COUIBottomSheetDialogFragment)?.dismiss()
    }

    override fun onDestroy() {
        super.onDestroy()
        releaseInner()
    }

    private fun releaseInner() {
        mPreferenceDialog.setOnChoiceSelectedCallBack(null)
        mViewModel = null
    }
}

class PlaySettingDialogPreferenceFragment : COUIPreferenceFragment() {
    companion object {
        private const val TAG = "PlaySettingDialogFragment"
        private const val KEY_SWITCH = "play_setting_switch_preference"
        private const val KEY_FOOTER = "play_setting_footer_preference"
    }

    private var mSwitchPreference: MySwitchPreference? = null
    private var mCallBack: OnChoiceSelectedCallBack? = null
    private var mFooterPreference: PlaySettingFooterPreference? = null
    private var mPlaySeed: TextView? = null
    private var mSectionSeekBar: COUISectionSeekBar? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val onCreateView = super.onCreateView(inflater, container, savedInstanceState)
        //去掉原本的toolbar
        onCreateView?.let {
            (it as? ViewGroup)?.apply {
                removeView(it.findViewById(R.id.appbar_layout))
            }
        }
        return onCreateView
    }

    override fun onCreatePreferences(savedInstanceState: Bundle?, rootKey: String?) {
        addPreferencesFromResource(R.xml.play_setting_dialog_preference)
        mSwitchPreference = findPreference(KEY_SWITCH)
        mFooterPreference = findPreference(KEY_FOOTER)
        mFooterPreference?.setListener(object : OnBindOrClickListener {
            override fun onBind(playSeed: TextView, sectionSeekBar: COUISectionSeekBar) {
                mPlaySeed = playSeed
                mSectionSeekBar = sectionSeekBar
                setRestoreTextViewEnable()
                (parentFragment as? PlaySettingDialogFragment)?.mViewModel?.let {
                    setChoiceSelected(it.playerController.playSpeedIndex.value ?: NumberConstant.NUM_1)
                }
            }

            override fun onProgressChanged(progress: Int) {
                setChoiceSelected(progress)
            }
        })
        mCallBack?.onPreferenceAdded()
    }

    fun setRestoreTextViewEnable() {
        val menuItemComplete = (parentFragment as? PlaySettingDialogFragment)?.menuItemComplete
        val menuItemReset = (parentFragment as? PlaySettingDialogFragment)?.menuItemReset
        val resetEnable = (parentFragment as? PlaySettingDialogFragment)?.getTextViewEnable() ?: false
        val completeEnable = (parentFragment as? PlaySettingDialogFragment)?.getCompleteBtnEnable() ?: false
        menuItemComplete?.isEnabled = completeEnable
        menuItemReset?.isEnabled = resetEnable
    }

    override fun onPreferenceTreeClick(preference: Preference?): Boolean {
        if (preference == null) {
            return super.onPreferenceTreeClick(preference)
        }
        when (preference) {
            mSwitchPreference -> {
                mCallBack?.onSwitchChanged(mSwitchPreference?.isChecked == true)
                checkDefaultSelected(mSectionSeekBar?.progress ?: 1)
            }
        }

        return super.onPreferenceTreeClick(preference)
    }

    fun setSwitchEnable(enable: Boolean) {
        mSwitchPreference?.setDataReady(enable)
    }

    fun setSwitchChecked(checked: Boolean) {
        mSwitchPreference?.isChecked = checked
    }

    fun setChoiceSelected(position: Int) {
        mSectionSeekBar?.progress = position
        mPlaySeed?.text = covertSeekBarValueToSpeed(position)
        mCallBack?.onSpeedChanged(position)
        checkDefaultSelected(position)
    }

    private fun covertSeekBarValueToSpeed(progress: Int): String {
        val result = when (progress) {
            NumberConstant.NUM_0 -> SPEED_HALF
            NumberConstant.NUM_1 -> SPEED_ONE
            NumberConstant.NUM_2 -> SPEED_ONE_HALF
            NumberConstant.NUM_3 -> SPEED_TWO
            else -> SPEED_ONE
        }
        DebugUtil.d(TAG, "covertPlaySpeedToSeekBar play progress $progress speed $result")
        return result
    }

    //检查当前的选项是否为默认选项，是则置灰“全部还原”按钮
    private fun checkDefaultSelected(position: Int) {
        if (mSwitchPreference?.isChecked == false && position == NumberConstant.NUM_1) {
            mCallBack?.onDefaultValueSelected()
        }
    }

    fun setOnChoiceSelectedCallBack(callBack: OnChoiceSelectedCallBack?) {
        mCallBack = callBack
    }

    override fun onDestroy() {
        super.onDestroy()
        mCallBack = null
    }

    interface OnChoiceSelectedCallBack {
        fun onPreferenceAdded()

        fun onCheckMuteEnable(): Boolean

        fun onSwitchChanged(checked: Boolean)

        fun onSpeedChanged(position: Int)

        fun onDefaultValueSelected()
    }
}