apply from: "../../common_flavor_build.gradle"
apply plugin: 'onative'

android {
    native_dependencies {
        artifact('com.coloros.recorderlibs:libsilenceWrapper:1.0.0-arm64-v8a:arm64-v8a')
        artifact('com.coloros.recorderlibs:libSlienceDetect:1.0.1-arm64-v8a:arm64-v8a')
    }
    namespace "com.soundrecorder.playback"
}

dependencies {
    implementation fileTree(include: ['*.so'], dir: 'libs')
    implementation libs.androidx.support
    implementation libs.androidx.appcompat
    //kotlin
    implementation libs.org.kotlin.stdlib
    implementation libs.androidx.lifecycle.livedata
    implementation libs.androidx.lifecycle.viewmodel
    implementation libs.androidx.lifecycle.extensions
    // fragment
    implementation libs.androidx.fragment.ktx
    implementation libs.gson
    kaptTest libs.androidx.databinding.compiler

    // base包为必须引用的包，prop_versionName需保持一致
    implementation (libs.oplus.coui.core) {
        exclude group: 'com.squareup.okio', module: 'okio'
    }
    // 以下子包应用可选使用，如有使用了如下子包的控件，则需要添加，未使用可以不引用
    implementation libs.oplus.coui.preference
    implementation libs.oplus.coui.segmentbutton
    implementation libs.oplus.coui.viewpager
    implementation libs.oplus.coui.recyclerview
    implementation libs.oplus.coui.dialog
    implementation libs.oplus.coui.toolbar
    implementation libs.oplus.coui.chip
    implementation libs.oplus.coui.scrollbar
    implementation libs.oplus.coui.poplist
    implementation libs.oplus.coui.bottomnavigation
    implementation libs.oplus.coui.snackbar
    implementation libs.oplus.coui.button
    implementation libs.oplus.coui.seekbar
    implementation libs.oplus.coui.progressbar
    implementation libs.oplus.coui.panel
    implementation libs.oplus.coui.responsiveui

    implementation(libs.oplus.material)
    compileOnly libs.oplus.addon
    testImplementation libs.oplus.addon
    domesticImplementation libs.oplus.cloudconfig.env
    domesticImplementation libs.oplus.cloudconfig
    debugImplementation libs.oplus.nearx.test.env

    // Koin for Android
    implementation(libs.koin)

    /*clodkit使用taphttp,为了解冲突，okhttp统一替换使用taphttp*/
    domesticImplementation(libs.heytap.nearx.http)

    implementation project(':common:RecorderLogBase')
    testImplementation project(':common:RecorderLogBase')
    implementation project(':common:modulerouter')
    implementation project(':common:libbase')
    implementation project(':common:libimageload')
    implementation project(':common:libcommon')
    implementation project(':component:player')
    implementation project(':component:wavemark')
    implementation project(':component:ConvertService')
    implementation libs.androidx.asynclayoutinflater
    implementation project(':component:summary')
}
