/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  RecorderWaveItemViewDrawAmplitudesTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/11/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.record.views.wave

import android.content.Context
import android.graphics.Canvas
import android.os.Build
import android.widget.LinearLayout
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.record.views.wave.anima.SpringInterpolator
import com.soundrecorder.record.views.wave.anima.StartRecordPathInterpolator
import com.soundrecorder.wavemark.wave.WaveViewUtil
import com.soundrecorder.wavemark.wave.view.WaveItemView
import org.junit.*
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.Mockito
import org.mockito.internal.verification.VerificationModeFactory
import org.mockito.invocation.InvocationOnMock
import org.mockito.stubbing.Answer
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config
import java.lang.reflect.InvocationTargetException

@RunWith(PowerMockRunner::class)
@PrepareForTest(WaveItemView::class, WaveViewUtil::class, DebugUtil::class)
@Config(sdk = [Build.VERSION_CODES.S])
class RecorderWaveItemViewDrawAmplitudesTest {

    @Before
    @Throws(java.lang.Exception::class)
    fun setUp() {
        PowerMockito.mockStatic(WaveViewUtil::class.java)
        PowerMockito.mockStatic(DebugUtil::class.java)
    }

    @Test
    @Throws(Exception::class)
    fun verify_runtimes_when_drawRecordAmplitude() {
        val waveItemView: RecorderWaveItemView = initRecordWaveItemView()
        val canvas = Mockito.mock(
            Canvas::class.java
        )
        //绘制波形方法
        val drawRecordAmplitude = PowerMockito.method(
            RecorderWaveItemView::class.java, "drawRecordAmplitude",
            Canvas::class.java
        )
        /*
         * 不测试没有数据的情况，因为waveItemView在调用drawRecordAmplitude的时候已经进行了判空,所以直接模拟有数据的情况
         * 1.模拟绘制虚线的情况,在屏幕中心线右侧
         */
        drawRecordAmplitude.invoke(waveItemView, canvas)
        PowerMockito.verifyPrivate(waveItemView, VerificationModeFactory.times(541))
            .invoke("getWaveLineHeight", ArgumentMatchers.anyInt(), ArgumentMatchers.anyInt())
    }

    @Ignore
    @Throws(InvocationTargetException::class, IllegalAccessException::class)
    private fun initRecordWaveItemView(): RecorderWaveItemView {
        val context = Mockito.mock(
            Context::class.java
        )
        var waveItemView: RecorderWaveItemView = PowerMockito.mock(RecorderWaveItemView::class.java)
        waveItemView = PowerMockito.spy(waveItemView)
        Whitebox.setInternalState(waveItemView, "mAmplitudeWidth", 1)
        Whitebox.setInternalState(waveItemView, "mVirtualAmpGap", 1)
        waveItemView.updateScreenWidth(1080)
        Assert.assertEquals(0, waveItemView.getWidth())
        Assert.assertEquals(
            540f,
            Whitebox.getInternalState(waveItemView, "mCenterLineX") as Float,
            0f
        )
        PowerMockito.doReturn(540).`when`(waveItemView).getWidth()
        Assert.assertEquals(540, waveItemView.getWidth())
        waveItemView.setCurViewIndex(1)
        Whitebox.setInternalState(waveItemView, "springInterpolator", SpringInterpolator())
        Whitebox.setInternalState(
            waveItemView,
            "recordPathInterpolator",
            StartRecordPathInterpolator()
        )
        val isReverseLayout = PowerMockito.method(WaveItemView::class.java, "isReverseLayout")
        val linearLayout = LinearLayout(context)
        PowerMockito.doReturn(linearLayout).`when`(waveItemView).getParent()
        PowerMockito.doReturn(context).`when`(waveItemView).getContext()
        //        PowerMockito.when(WaveViewUtil.getTimeLineGap(context)).thenReturn(1F)
        PowerMockito.`when`(WaveViewUtil.getTimeLineGap(ArgumentMatchers.any())).thenAnswer(
            Answer { invocation: InvocationOnMock? -> 1f } as Answer<Float>)
        Assert.assertEquals(1f, WaveViewUtil.getTimeLineGap(context), 0f)
        PowerMockito.`when`(WaveViewUtil.getStringBySecond(ArgumentMatchers.anyInt())).thenAnswer(
            Answer { invocation: InvocationOnMock? -> "111" } as Answer<String>)
        Assert.assertEquals("111", WaveViewUtil.getStringBySecond(123))

        //设置rtl
        val isRtl = isReverseLayout.invoke(waveItemView) as Boolean
        Assert.assertFalse(isRtl)
        Assert.assertEquals(linearLayout, waveItemView.getParent())
        //准备波形数据
        val amplitudes: MutableList<Int> = ArrayList()
        for (i in 0..599) {
            amplitudes.add(i)
        }
        Whitebox.setInternalState(waveItemView, "mAmplitudes", amplitudes)
        return waveItemView
    }
}