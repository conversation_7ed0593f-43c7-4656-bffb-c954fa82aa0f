/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: - CustomCOUIRecyclerView.kt
 * Description:
 *     The helper class for RecyclerView ui showing subtitle in record pat.
 *
 * Version: 1.0
 * Date: 2025-06-26
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2025-06-26   1.0    Create this module
 *********************************************************************************/
package com.soundrecorder.record.views

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager

class CustomCOUIRecyclerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : COUIRecyclerView(context, attrs, defStyleAttr) {

    private var isUserScrolling = false
    private var isContentOverflow = false
    private val layoutChangeListener = OnLayoutChangeListener { _, _, _, _, _, _, _, _, _ ->
        checkContentOverflow()
    }

    init {
        isVerticalScrollBarEnabled = false
        addOnLayoutChangeListener(layoutChangeListener)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        removeCallbacks(hideScrollBarRunnable)
        removeOnLayoutChangeListener(layoutChangeListener)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(ev: MotionEvent): Boolean {
        when (ev.action) {
            MotionEvent.ACTION_DOWN -> {
                isUserScrolling = true
                if (isContentOverflow) showScrollBar()
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                isUserScrolling = false
                removeCallbacks(hideScrollBarRunnable)
                postDelayed(hideScrollBarRunnable, Companion.SCROLLHIDEDELAYTIME)
            }
        }
        return super.onTouchEvent(ev)
    }

    private val hideScrollBarRunnable = Runnable { hideScrollBarIfNotScrolling() }

    private fun checkContentOverflow() {
        // 移除post延迟，直接在主线程检查
        isContentOverflow = (layoutManager as? LinearLayoutManager)?.let {
            it.itemCount > 0 && it.findLastVisibleItemPosition() < it.itemCount - 1
        } ?: false
    }


    private fun showScrollBar() {
        isVerticalScrollBarEnabled = true
        awakenScrollBars()
    }

    private fun hideScrollBarIfNotScrolling() {
        if (!isUserScrolling) {
            isVerticalScrollBarEnabled = false
            invalidate()
        }
    }

    override fun onScrollChanged(l: Int, t: Int, oldl: Int, oldt: Int) {
        super.onScrollChanged(l, t, oldl, oldt)
        if (isUserScrolling && isContentOverflow) showScrollBar()
    }

    companion object {
        private const val SCROLLHIDEDELAYTIME = 1000L
    }
}